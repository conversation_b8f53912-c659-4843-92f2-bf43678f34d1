# reactive_app.py
from flexx import flx

class ReactiveApp(flx.Widget):
    def init(self):
        super().init()
        self.count = 0
        with flx.VBox(style="padding:20px;"):
            self.lbl = flx.Label(text="0", style="font-size:30px;")
            with flx.HBox():
                flx.<PERSON><PERSON>(text="Increment", on_click=lambda *e: self.set_count(self.count + 1))
                flx.Button(text="Reset", on_click=lambda *e: self.set_count(0))

    @flx.reaction('count')
    def _update_label(self, *events):
        self.lbl.set_text(str(self.count))

if __name__ == "__main__":
    app = flx.App(ReactiveApp, title="Reactive Flexx")
    app.launch()
    flx.run()
